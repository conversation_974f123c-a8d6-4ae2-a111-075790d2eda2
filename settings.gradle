rootProject.name = 'grpc-spring-boot-starter'

include "grpc-common-spring-boot"
include "grpc-client-spring-boot-autoconfigure"
include "grpc-client-spring-boot-starter"
include "grpc-server-spring-boot-autoconfigure"
include "grpc-server-spring-boot-starter"

include "tests"

// examples
include "examples:grpc-lib"
include "examples:local-grpc-client"
include "examples:local-grpc-server"
include "examples:cloud-eureka-server"
include "examples:cloud-grpc-client"
include "examples:cloud-grpc-server"
include "examples:security-grpc-client"
include "examples:security-grpc-server"
