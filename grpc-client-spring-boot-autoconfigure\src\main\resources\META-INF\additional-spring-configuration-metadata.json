{"groups": [{"name": "grpc", "type": "net.devh.boot.grpc.client.config.GrpcChannelsProperties", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelsProperties", "description": "The container for grpc related configuration options."}, {"name": "grpc.client", "type": "java.util.Map<java.lang.String,net.devh.boot.grpc.client.config.GrpcChannelProperties>", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelsProperties", "sourceMethod": "getClient()", "description": "A container for named channel properties.\nEach channel has its own configuration. If you try to get a channel that does not have a configuration yet, it will be created. If something is not configured in the channel properties, it will be copied from the global config during the first retrieval. If some property is configured in neither the channel properties nor the global properties then a default value will be used."}, {"name": "grpc.client.GLOBAL", "type": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The channel properties for a single named gRPC channel or service reference. In this case the GLOBAL channel."}, {"name": "grpc.client.GLOBAL.security", "type": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "sourceMethod": "getSecurity()", "description": "A container with options for the channel's transport security. In this case the GLOBAL channel's ones."}], "properties": [{"name": "grpc.client", "type": "java.util.Map<java.lang.String,net.devh.boot.grpc.client.config.GrpcChannelProperties>", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelsProperties"}, {"name": "grpc.client.GLOBAL.address", "type": "java.net.URI", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The target address uri for the channel.\nThe target uri must be in the format: schema:[//[authority]][/path].\nIf nothing is configured then the io.grpc.NameResolver.Factory will decide on the default.\nExamples:\n\t\"static://*********:9090,***********:9091\",\n\t\"dns:/example.com:9090\",\n\t\"discovery:/foo-service\""}, {"name": "grpc.client.GLOBAL.default-load-balancing-policy", "type": "java.lang.String", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The default load balancing policy this channel should use.", "defaultValue": "round_robin"}, {"name": "grpc.client.GLOBAL.enable-keep-alive", "type": "java.lang.Bo<PERSON>an", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "Whether keepAlive should be enabled.", "defaultValue": false}, {"name": "grpc.client.GLOBAL.full-stream-decompression", "type": "java.lang.Bo<PERSON>an", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "Whether full-stream decompression of inbound streams should be enabled.", "defaultValue": false}, {"name": "grpc.client.GLOBAL.keep-alive-time", "type": "java.time.Duration", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The default delay before we send a keepAlive.\nDefault unit is seconds.", "defaultValue": "60s"}, {"name": "grpc.client.GLOBAL.keep-alive-timeout", "type": "java.time.Duration", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The default timeout for a keepAlives ping request.\nDefault unit is seconds.", "defaultValue": "20s"}, {"name": "grpc.client.GLOBAL.keep-alive-without-calls", "type": "java.lang.Bo<PERSON>an", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "Whether keepAlive will be performed when there are no outstanding RPC on a connection.", "defaultValue": false}, {"name": "grpc.client.GLOBAL.shutdown-grace-period", "type": "java.time.Duration", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The time to wait for the channel to gracefully shutdown (completing all requests).\nIf set to a negative value, the channel waits forever.\nIf set to 0 the channel will force shutdown immediately.\nDefaults to 30s.", "defaultValue": "30s"}, {"name": "grpc.client.GLOBAL.max-inbound-message-size", "type": "org.springframework.util.unit.DataSize", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The maximum message size allowed to be received by the channel.\nIf not set (null) then it will default to gRPC's default.\nIf set to -1 then it will use the highest possible limit (not recommended)."}, {"name": "grpc.client.GLOBAL.negotiation-type", "type": "net.devh.boot.grpc.client.config.NegotiationType", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "The negotiation type to use on the connection.", "defaultValue": "TLS"}, {"name": "grpc.client.GLOBAL.immediate-connect-timeout", "type": "java.time.Duration", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties", "description": "Connection timeout at application startup. If set to a positive duration instructs a client to connect to GRPC-endpoint when GRPC stub is created.", "defaultValue": 0}, {"name": "grpc.client.GLOBAL.security.authority-override", "type": "java.lang.String", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The authority to check for during server certificate verification. By default the clients will use the name of the client to check the server certificate's common + alternative names."}, {"name": "grpc.client.GLOBAL.security.certificate-chain", "type": "org.springframework.core.io.Resource", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The path to SSL certificate chain. Required if \"client-auth-enabled\" is enabled.\nThe linked certificate will be used to authenticate the client."}, {"name": "grpc.client.GLOBAL.security.ciphers", "type": "java.util.List<java.lang.String>", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The cipher suite accepted for secure connections (in the order of preference). If not specified (null), then the default suites will be used."}, {"name": "grpc.client.GLOBAL.security.client-auth-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "Whether client can authenticate using certificates.", "defaultValue": false}, {"name": "grpc.client.GLOBAL.security.private-key", "type": "org.springframework.core.io.Resource", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The path to the private key. Required if \"client-auth-enabled\" is enabled."}, {"name": "grpc.client.GLOBAL.security.private-key-password", "type": "java.lang.String", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The password for the private key."}, {"name": "grpc.client.GLOBAL.security.protocols", "type": "java.lang.String", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The TLS protocols accepted for secure connections. If not specified (null), then the default ones will be used."}, {"name": "grpc.client.GLOBAL.security.trust-cert-collection", "type": "org.springframework.core.io.Resource", "sourceType": "net.devh.boot.grpc.client.config.GrpcChannelProperties$Security", "description": "The path to the trusted certificate collection.\nIf not set (null) it will use the system's default collection (Default).\nThis collection will be used to verify server certificates."}]}