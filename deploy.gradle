apply plugin: 'maven-publish'
apply plugin: 'signing'

ext {
    isReleaseVersion = !(projectVersion =~ /-SNAPSHOT$/)
    isNeedSign = project.hasProperty('signing.gnupg.keyName') && isReleaseVersion
}

task sourcesJar(type: Jar) {
    from sourceSets.main.allJava
    archiveClassifier.set('sources')
}

task javadocJar(type: Jar) {
    from javadoc
    archiveClassifier.set('javadoc')
}

publishing {
    publications {
        mavenJava(MavenPublication) {

            from components.java
            artifact sourcesJar
            artifact javadocJar

            pom {
                name = 'gRPC Spring Boot Starter'
                description = 'gRPC Spring Boot Starter'
                url = 'https://github.com/yidongnan/grpc-spring-boot-starter'
                licenses {
                    license {
                        name = 'MIT License'
                        url = 'http://www.opensource.org/licenses/mit-license.php'
                        distribution = 'repo'
                    }
                }
                developers {
                    developer {
                        id = 'yidongnan'
                        name = '<PERSON>'
                        email = '<EMAIL>'
                    }
                    developer {
                        id = 'ST-DDT'
                        name = '<PERSON>'
                        email = '<EMAIL>'
                        organization = 'Aequitas Software GmbH & Co. KG'
                        organizationUrl = 'https://aequitas-software.de/'
                    }
                }
                scm {
                    connection = 'scm:git:git://github.com/yidongnan/grpc-spring-boot-starter.git'
                    developerConnection = 'scm:git:<EMAIL>:yidongnan/grpc-spring-boot-starter.git'
                    url = 'https://github.com/yidongnan/grpc-spring-boot-starter'
                }
            }

            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionResult()
                }
            }
        }
    }
    repositories {
        maven {
            credentials {
                username System.getenv('OSSRH_USER')
                password System.getenv('OSSRH_PASS')
            }
            if (project.ext.isReleaseVersion) {
                url "https://oss.sonatype.org/service/local/staging/deploy/maven2"
            } else {
                url "https://oss.sonatype.org/content/repositories/snapshots"
            }
        }
    }

    tasks.withType(Sign) {
        onlyIf { project.ext.isNeedSign }
    }

    signing {
        useGpgCmd()
        sign publishing.publications.mavenJava
    }

    javadoc {
        if(JavaVersion.current().isJava9Compatible()) {
            options.addBooleanOption('html5', true)
        }
    }
}
