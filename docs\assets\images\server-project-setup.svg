<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: g Pages: 1 -->
<svg width="811pt" height="207pt"
 viewBox="0.00 0.00 811.18 207.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 203)">
<title>g</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-203 807.185,-203 807.185,4 -4,4"/>
<g id="clust1" class="cluster"><title>cluster_interface</title>
<polygon fill="none" stroke="black" points="8,-8 8,-191 543.185,-191 543.185,-8 8,-8"/>
<text text-anchor="middle" x="275.592" y="-175.8" font-family="Times New Roman,serif" font-size="14.00">Interface&#45;Project</text>
</g>
<g id="clust3" class="cluster"><title>cluster_server</title>
<polygon fill="none" stroke="black" points="563.185,-96 563.185,-171 795.185,-171 795.185,-96 563.185,-96"/>
<text text-anchor="middle" x="679.185" y="-155.8" font-family="Times New Roman,serif" font-size="14.00">Server&#45;Project</text>
</g>
<g id="clust4" class="cluster"><title>cluster_clients</title>
<polygon fill="none" stroke="#7f7f7f" points="563.185,-13 563.185,-88 795.185,-88 795.185,-13 563.185,-13"/>
<text text-anchor="middle" x="679.185" y="-72.8" font-family="Times New Roman,serif" font-size="14.00" fill="#7f7f7f">Client&#45;Projects</text>
</g>
<!-- protofile -->
<g id="node1" class="node"><title>protofile</title>
<g id="a_node1"><a xlink:href="https://developers.google.com/protocol-buffers/docs/proto3#simple" xlink:title="protobuf&#45;file" target="_blank">
<polygon fill="none" stroke="black" points="108,-160 21,-160 21,-124 108,-124 108,-160"/>
<text text-anchor="middle" x="64.5" y="-138.3" font-family="Times New Roman,serif" font-size="14.00">protobuf&#45;file</text>
</a>
</g>
</g>
<!-- protoc -->
<g id="node4" class="node"><title>protoc</title>
<g id="a_node4"><a xlink:href="https://mvnrepository.com/artifact/com.google.protobuf/protoc" xlink:title="protobuf&#45;compiler" target="_blank">
<ellipse fill="none" stroke="black" cx="236.092" cy="-88" rx="77.1866" ry="18"/>
<text text-anchor="middle" x="236.092" y="-84.3" font-family="Times New Roman,serif" font-size="14.00">protobuf&#45;compiler</text>
</a>
</g>
</g>
<!-- protofile&#45;&gt;protoc -->
<g id="edge1" class="edge"><title>protofile&#45;&gt;protoc:w</title>
<path fill="none" stroke="black" d="M97.5205,-123.988C102.712,-121.013 108.017,-117.941 113,-115 129.388,-105.328 133.113,-93.1589 146.959,-89.2615"/>
<polygon fill="black" stroke="black" points="147.601,-92.7087 157.092,-88 146.736,-85.7623 147.601,-92.7087"/>
</g>
<!-- protofile2 -->
<g id="node2" class="node"><title>protofile2</title>
<g id="a_node2"><a xlink:href="https://developers.google.com/protocol-buffers/docs/proto3#services" xlink:title="protobuf&#45;file2" target="_blank">
<polygon fill="none" stroke="#7f7f7f" points="111.5,-106 17.5,-106 17.5,-70 111.5,-70 111.5,-106"/>
<text text-anchor="middle" x="64.5" y="-84.3" font-family="Times New Roman,serif" font-size="14.00" fill="#7f7f7f">protobuf&#45;file2</text>
</a>
</g>
</g>
<!-- protofile2&#45;&gt;protoc -->
<g id="edge2" class="edge"><title>protofile2&#45;&gt;protoc:w</title>
<path fill="none" stroke="#7f7f7f" d="M111.626,-88C122.877,-88 135.076,-88 146.868,-88"/>
<polygon fill="#7f7f7f" stroke="#7f7f7f" points="147.092,-91.5001 157.092,-88 147.092,-84.5001 147.092,-91.5001"/>
</g>
<!-- protofileN -->
<g id="node3" class="node"><title>protofileN</title>
<g id="a_node3"><a xlink:href="https://developers.google.com/protocol-buffers/docs/javatutorial" xlink:title="protobuf&#45;fileN">
<polygon fill="none" stroke="#bfbfbf" points="113,-52 16,-52 16,-16 113,-16 113,-52"/>
<text text-anchor="middle" x="64.5" y="-30.3" font-family="Times New Roman,serif" font-size="14.00" fill="#bfbfbf">protobuf&#45;fileN</text>
</a>
</g>
</g>
<!-- protofileN&#45;&gt;protoc -->
<g id="edge3" class="edge"><title>protofileN&#45;&gt;protoc:w</title>
<path fill="none" stroke="#bfbfbf" d="M97.5205,-52.0124C102.712,-54.9867 108.017,-58.0591 113,-61 129.388,-70.6719 133.113,-82.8411 146.959,-86.7385"/>
<polygon fill="#bfbfbf" stroke="#bfbfbf" points="146.736,-90.2377 157.092,-88 147.601,-83.2913 146.736,-90.2377"/>
</g>
<!-- grpcc -->
<g id="node5" class="node"><title>grpcc</title>
<g id="a_node5"><a xlink:href="https://mvnrepository.com/artifact/io.grpc/protoc-gen-grpc-java" xlink:title="protoc&#45;gen&#45;grpc&#45;java" target="_blank">
<ellipse fill="none" stroke="black" cx="236.092" cy="-34" rx="87.1846" ry="18"/>
<text text-anchor="middle" x="236.092" y="-30.3" font-family="Times New Roman,serif" font-size="14.00">protoc&#45;gen&#45;grpc&#45;java</text>
</a>
</g>
</g>
<!-- protoc&#45;&gt;grpcc -->
<g id="edge4" class="edge"><title>protoc&#45;&gt;grpcc</title>
<path fill="none" stroke="black" d="M285.959,-74.1325C302.104,-67.2718 305.598,-60.4111 296.439,-53.5504"/>
<polygon fill="black" stroke="black" points="297.967,-50.3947 287.534,-48.5547 294.543,-56.4997 297.967,-50.3947"/>
</g>
<!-- servicemodel -->
<g id="node6" class="node"><title>servicemodel</title>
<g id="a_node6"><a xlink:href="https://github.com/grpc/grpc-java/blob/master/README.md#generated-code" xlink:title="service and model defintions" target="_blank">
<polygon fill="none" stroke="black" points="535.185,-106 359.185,-106 359.185,-70 535.185,-70 535.185,-106"/>
<text text-anchor="middle" x="447.185" y="-84.3" font-family="Times New Roman,serif" font-size="14.00">service and model defintions</text>
</a>
</g>
</g>
<!-- protoc&#45;&gt;servicemodel -->
<g id="edge6" class="edge"><title>protoc&#45;&gt;servicemodel</title>
<path fill="none" stroke="black" d="M313.467,-88C324.926,-88 336.873,-88 348.699,-88"/>
<polygon fill="black" stroke="black" points="349.045,-91.5001 359.045,-88 349.045,-84.5001 349.045,-91.5001"/>
</g>
<!-- grpcc&#45;&gt;protoc -->
<g id="edge5" class="edge"><title>grpcc&#45;&gt;protoc</title>
<path fill="none" stroke="black" d="M184.651,-48.5547C169.053,-55.5486 166.603,-62.5426 177.302,-69.5365"/>
<polygon fill="black" stroke="black" points="175.733,-72.6652 186.226,-74.1325 178.938,-66.4421 175.733,-72.6652"/>
</g>
<!-- serviceimpl -->
<g id="node7" class="node"><title>serviceimpl</title>
<g id="a_node7"><a xlink:href="https://github.com/yidongnan/grpc-spring-boot-starter/blob/master/grpc-server-spring-boot-autoconfigure/src/main/java/net/devh/boot/grpc/server/service/GrpcService.java#L49" xlink:title="Service implementations" target="_blank">
<ellipse fill="none" stroke="black" cx="679.185" cy="-122" rx="108" ry="18"/>
<text text-anchor="middle" x="679.185" y="-118.3" font-family="Times New Roman,serif" font-size="14.00">Service implementations</text>
</a>
</g>
</g>
<!-- servicemodel&#45;&gt;serviceimpl -->
<g id="edge7" class="edge"><title>servicemodel&#45;&gt;serviceimpl</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M545.624,-102.404C563.005,-104.974 580.935,-107.624 597.774,-110.113"/>
<polygon fill="black" stroke="black" points="545.87,-98.9025 535.466,-100.902 544.846,-105.827 545.87,-98.9025"/>
</g>
<!-- clientfield -->
<g id="node8" class="node"><title>clientfield</title>
<g id="a_node8"><a xlink:href="https://github.com/yidongnan/grpc-spring-boot-starter/blob/master/grpc-client-spring-boot-autoconfigure/src/main/java/net/devh/boot/grpc/client/inject/GrpcClient.java#L69" xlink:title="Client/Stub usage" target="_blank">
<ellipse fill="none" stroke="#7f7f7f" cx="679.185" cy="-39" rx="108" ry="18"/>
<text text-anchor="middle" x="679.185" y="-35.3" font-family="Times New Roman,serif" font-size="14.00" fill="#7f7f7f">Client/Stub usage</text>
</a>
</g>
</g>
<!-- servicemodel&#45;&gt;clientfield -->
<g id="edge8" class="edge"><title>servicemodel:se&#45;&gt;clientfield</title>
<path fill="none" stroke="#7f7f7f" stroke-dasharray="5,2" d="M542.442,-62.1048C550.971,-52.3493 560.796,-44.7946 571.181,-39.0639"/>
<polygon fill="#7f7f7f" stroke="#7f7f7f" points="539.653,-59.9889 536.185,-70 545.139,-64.3368 539.653,-59.9889"/>
</g>
<!-- servicemodel&#45;&gt;clientfield -->
<g id="edge9" class="edge"><title>servicemodel:se&#45;&gt;clientfield</title>
<path fill="none" stroke="#7f7f7f" stroke-dasharray="5,2" d="M543.937,-63.1686C554.091,-55.2694 565.961,-49.6315 578.367,-45.6514"/>
<polygon fill="#7f7f7f" stroke="#7f7f7f" points="541.373,-60.7627 536.185,-70 546.001,-66.0146 541.373,-60.7627"/>
</g>
<!-- servicemodel&#45;&gt;clientfield -->
<g id="edge10" class="edge"><title>servicemodel:se&#45;&gt;clientfield</title>
<path fill="none" stroke="#7f7f7f" stroke-dasharray="5,2" d="M545.316,-65.2844C562.694,-57.6253 584.802,-54.2643 605.975,-52.3086"/>
<polygon fill="#7f7f7f" stroke="#7f7f7f" points="543.464,-62.3017 536.185,-70 546.676,-68.5214 543.464,-62.3017"/>
</g>
</g>
</svg>
