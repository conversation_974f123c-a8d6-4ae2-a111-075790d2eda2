/*
 * Copyright (c) 2016-2021 <PERSON> <yido<PERSON><PERSON>@gmail.com>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
 * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package net.devh.boot.grpc.test.config;

import static net.devh.boot.grpc.server.security.check.AccessPredicate.SocketPredicate.inProcess;
import static net.devh.boot.grpc.server.security.check.AccessPredicate.SocketPredicate.inet;
import static net.devh.boot.grpc.server.security.check.AccessPredicate.fromClientAddress;
import static net.devh.boot.grpc.server.security.check.AccessPredicate.hasRole;
import static net.devh.boot.grpc.server.security.check.AccessPredicate.permitAll;
import static net.devh.boot.grpc.server.security.check.AccessPredicate.toServerAddress;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.vote.UnanimousBased;

import net.devh.boot.grpc.server.security.check.AccessPredicateVoter;
import net.devh.boot.grpc.server.security.check.GrpcSecurityMetadataSource;
import net.devh.boot.grpc.server.security.check.ManualGrpcSecurityMetadataSource;
import net.devh.boot.grpc.test.proto.TestServiceGrpc;

@Configuration
public class ManualSecurityConfiguration {

    @Bean
    AccessDecisionManager accessDecisionManager() {
        final List<AccessDecisionVoter<?>> voters = new ArrayList<>();
        voters.add(new AccessPredicateVoter());
        return new UnanimousBased(voters);
    }

    @Bean
    GrpcSecurityMetadataSource grpcSecurityMetadataSource() {
        final ManualGrpcSecurityMetadataSource source = new ManualGrpcSecurityMetadataSource();
        source.set(TestServiceGrpc.getSecureMethod(),
                hasRole("ROLE_CLIENT1").and(fromClientAddress(inProcess().or(inet()))));
        source.set(TestServiceGrpc.getSecureDrainMethod(),
                hasRole("ROLE_CLIENT1").and(fromClientAddress(inProcess("test").or(inet()))));
        source.set(TestServiceGrpc.getSecureSupplyMethod(),
                hasRole("ROLE_CLIENT1").or(toServerAddress(inProcess("test-secondary"))));
        source.set(TestServiceGrpc.getSecureBidiMethod(), hasRole("ROLE_CLIENT1"));
        source.setDefault(permitAll());
        return source;
    }

}
