/*
 * Copyright (c) 2016-2021 <PERSON> <yido<PERSON><PERSON>@gmail.com>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
 * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package net.devh.boot.grpc.server.security.interceptors;

import static java.util.Objects.requireNonNull;

import org.springframework.core.annotation.Order;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.SecurityMetadataSource;
import org.springframework.security.access.intercept.AbstractSecurityInterceptor;
import org.springframework.security.access.intercept.InterceptorStatusToken;
import org.springframework.security.core.AuthenticationException;

import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.common.util.InterceptorOrder;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import net.devh.boot.grpc.server.security.check.GrpcSecurityMetadataSource;

/**
 * A server interceptor that will check the security context whether it has permission to access the grpc method. This
 * interceptor uses a {@link GrpcSecurityMetadataSource} to obtain the information how the called method is protected
 * and uses an {@link AccessDecisionManager} to evaluate that information. This interceptor isn't needed if you use
 * spring's security annotations, but can be used additionally. An example use case of using both would be requiring all
 * users to be authenticated, while using the annotations to require further permissions.
 *
 * <p>
 * <b>Note:</b> If you use spring's security annotations, the you have to use
 * {@code @EnableGlobalMethodSecurity(proxyTargetClass = true, ...)}
 * </p>
 *
 * <AUTHOR> Theuke (<EMAIL>)
 */
@Slf4j
@GrpcGlobalServerInterceptor
@Order(InterceptorOrder.ORDER_SECURITY_AUTHORISATION)
public class AuthorizationCheckingServerInterceptor extends AbstractSecurityInterceptor implements ServerInterceptor {

    private final GrpcSecurityMetadataSource securityMetadataSource;

    /**
     * Creates a new AuthorizationCheckingServerInterceptor with the given {@link AccessDecisionManager} and
     * {@link GrpcSecurityMetadataSource}.
     *
     * @param accessDecisionManager The access decision manager to use.
     * @param securityMetadataSource The security metadata source to use.
     */
    public AuthorizationCheckingServerInterceptor(final AccessDecisionManager accessDecisionManager,
            final GrpcSecurityMetadataSource securityMetadataSource) {
        setAccessDecisionManager(requireNonNull(accessDecisionManager, "accessDecisionManager"));
        this.securityMetadataSource = requireNonNull(securityMetadataSource, "securityMetadataSource");
    }

    @SuppressWarnings("unchecked")
    @Override
    public <ReqT, RespT> Listener<ReqT> interceptCall(
            final ServerCall<ReqT, RespT> call,
            final Metadata headers,
            final ServerCallHandler<ReqT, RespT> next) {

        final InterceptorStatusToken token;
        try {
            token = beforeInvocation(call);
        } catch (final AuthenticationException | AccessDeniedException e) {
            log.debug("Access denied");
            throw e;
        }
        log.debug("Access granted");
        final Listener<ReqT> result;
        try {
            result = next.startCall(call, headers);
        } finally {
            finallyInvocation(token);
        }
        return (Listener<ReqT>) afterInvocation(token, result);
    }

    @Override
    public Class<?> getSecureObjectClass() {
        return ServerCall.class;
    }

    @Override
    public SecurityMetadataSource obtainSecurityMetadataSource() {
        return this.securityMetadataSource;
    }

}
