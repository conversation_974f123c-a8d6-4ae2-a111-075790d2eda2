name: Build master branch

on:
  push:
    branches:
      - master

jobs:
  build:
    name: Build on java ${{ matrix.java }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: ['8', '11']
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v1

      - name: Set up java ${{ matrix.java }}
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: ${{ matrix.java }}
          check-latest: true

      - name: Decrypt file
        run: openssl aes-256-cbc -K ${{ secrets.ENCRYPTED_KEY }} -iv ${{ secrets.ENCRYPTED_IV }} -in private.key.enc -out ./private.key -d && gpg --batch --import ./private.key || echo

      - name: Build with Gradle
        uses: gradle/gradle-build-action@v2
        with:
          arguments: --scan --stacktrace --warning-mode=all build

      - name: Deploy with Gradle
        if: ${{ matrix.java == '8' }}
        uses: gradle/gradle-build-action@v2
        with:
          arguments: --scan publish -x check -Psigning.gnupg.executable=gpg -Psigning.gnupg.keyName=${{ secrets.GPG_NAME }} -Psigning.gnupg.passphrase=${{ secrets.GPG_PASSWORD }}
        env:
          OSSRH_USER: ${{ secrets.OSSRH_USER }}
          OSSRH_PASS: ${{ secrets.OSSRH_PASS }}
