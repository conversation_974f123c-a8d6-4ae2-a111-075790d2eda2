<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<configuration packagingData="true">

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%highlight(%d{HH:mm:ss.SSS} [%10thread] %-5level %logger{36} - %msg%n)</pattern>
        </encoder>
    </appender>

    <logger name="net.devh.boot.grpc.common" level="debug" />
    <logger name="net.devh.boot.grpc.server" level="debug" />
    <root level="info">
        <appender-ref ref="STDOUT" />
    </root>

</configuration>
