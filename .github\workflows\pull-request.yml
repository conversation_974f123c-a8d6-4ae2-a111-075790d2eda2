name: Pull Request CI

on: ['pull_request']

jobs:
  build:
    name: Build on java ${{ matrix.java }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: ['8', '11']
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v1

      - name: Set up java ${{ matrix.java }}
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: ${{ matrix.java }}
          check-latest: true

      - name: Build with Gradle
        uses: gradle/gradle-build-action@v2
        with:
          arguments: --scan --stacktrace --warning-mode=all build

      # Avoid publish errors when upgrading gradle version and dependencyManager plugin
      - name: Try publishToMavenLocal
        uses: gradle/gradle-build-action@v2
        with:
          arguments: publishToMavenLocal
