plugins {
    id 'java-library'
}

apply from: '../deploy.gradle'

group = 'net.devh'
version = projectVersion

compileJava.dependsOn(processResources)

dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-autoconfigure-processor'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    api project(':grpc-common-spring-boot')
    api 'org.springframework.boot:spring-boot-starter'
    optionalSupportImplementation 'org.springframework.boot:spring-boot-starter-actuator'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    optionalSupportImplementation 'io.zipkin.brave:brave-instrumentation-grpc'
    optionalSupportImplementation 'javax.inject:javax.inject:1'
    // api comes from java-library, and allows exposure of the dependency to consumers of the library
    //  this means it can be used implicitly without specifying the dependency (unless you wish to override with a different version, or exclude)
    optionalSupportApi 'io.grpc:grpc-netty'
    optionalSupportApi 'io.netty:netty-transport-native-epoll'
    api 'io.grpc:grpc-inprocess'
    api 'io.grpc:grpc-netty-shaded'
    api 'io.grpc:grpc-protobuf'
    api 'io.grpc:grpc-stub'

    testImplementation 'io.grpc:grpc-testing'
    testImplementation('org.springframework.boot:spring-boot-starter-test')
}
