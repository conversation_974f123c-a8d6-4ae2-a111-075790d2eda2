plugins {
    id 'org.springframework.boot'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.security:spring-security-core'
    implementation project(':grpc-client-spring-boot-starter') // replace to implementation "net.devh:grpc-client-spring-boot-starter:${springBootGrpcVersion}"
    implementation project(':examples:grpc-lib')
}
