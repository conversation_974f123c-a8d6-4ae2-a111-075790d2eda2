# Handle line endings automatically for files detected as text
# and leave all files detected as binary untouched.
*               text=auto

#
# The above will handle all files NOT found below
#
# These files are text and should be normalized (Convert crlf => lf)
*.bash          text eol=lf
*.bat           text eol=crlf
*.df            text
*.java          text diff=java
*.js            text
*.json          text
*.properties    text
# ensure that sh files can be run using git-bash or wsl even if pulled on Windows from the repo
*.sh            text eol=lf
*.txt           text
*.xml           text
*.yml           text
*.yaml          text
*.md            text

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.gz            binary
*.class         binary
*.dll           binary
*.ear           binary
*.gif           binary
*.ico           binary
*.jar           binary
*.jpg           binary
*.jpeg          binary
*.png           binary
*.so            binary
*.war           binary
*.p12           binary
*.zip           binary
