plugins {
    id 'java-library'
}

apply from: '../deploy.gradle'

group = 'net.devh'
version = projectVersion

compileJava.dependsOn(processResources)

dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-autoconfigure-processor'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    api project(':grpc-common-spring-boot')
    api 'org.springframework.boot:spring-boot-starter'
    optionalSupportImplementation 'org.springframework.boot:spring-boot-starter-actuator'
    optionalSupportImplementation 'org.springframework.security:spring-security-core'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-zookeeper-discovery'
    optionalSupportImplementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    optionalSupportImplementation "com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery"
    optionalSupportImplementation 'io.zipkin.brave:brave-instrumentation-grpc'
    optionalSupportApi 'io.grpc:grpc-netty'
    api 'io.grpc:grpc-inprocess'
    api 'io.grpc:grpc-netty-shaded'
    api 'io.grpc:grpc-protobuf'
    api 'io.grpc:grpc-stub'
    api 'io.grpc:grpc-services'
    api 'io.grpc:grpc-api'

    testImplementation 'io.grpc:grpc-testing'
    testImplementation('org.springframework.boot:spring-boot-starter-test')
}
